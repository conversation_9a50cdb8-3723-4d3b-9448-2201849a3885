'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { useForm, zodResolver } from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import { useToast } from '@repo/design-system/components/ui/use-toast';
import { Plus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import * as z from 'zod';
import {
  addEmailToBlacklist,
  getBlacklistedEmails,
  removeEmailFromBlacklist,
} from './actions';

// Form validation schema
const addEmailSchema = z.object({
  email: z
    .string()
    .min(1, 'Email address is required')
    .email('Please enter a valid email address')
    .toLowerCase(),
});

type AddEmailFormValues = z.infer<typeof addEmailSchema>;

export function BlacklistSettings() {
  const [blacklistedEmails, setBlacklistedEmails] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Initialize form with react-hook-form
  const form = useForm<AddEmailFormValues>({
    resolver: zodResolver(addEmailSchema),
    defaultValues: {
      email: '',
    },
  });

  useEffect(() => {
    loadBlacklistedEmails();
  }, []);

  const loadBlacklistedEmails = async () => {
    try {
      const emails = await getBlacklistedEmails();
      setBlacklistedEmails(emails);
    } catch (error) {
      console.error('Failed to load blacklisted emails:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddEmail = async () => {
    if (!newEmail.trim() || !isValidEmail(newEmail)) {
      alert('Please enter a valid email address');
      return;
    }

    if (blacklistedEmails.includes(newEmail.toLowerCase())) {
      alert('This email is already blacklisted');
      return;
    }

    setIsAdding(true);
    try {
      await addEmailToBlacklist(newEmail.toLowerCase());
      setBlacklistedEmails([...blacklistedEmails, newEmail.toLowerCase()]);
      setNewEmail('');
    } catch (error) {
      console.error('Failed to add email to blacklist:', error);
      alert('Failed to add email to blacklist');
    } finally {
      setIsAdding(false);
    }
  };

  const handleRemoveEmail = async (email: string) => {
    try {
      await removeEmailFromBlacklist(email);
      setBlacklistedEmails(blacklistedEmails.filter((e) => e !== email));
    } catch (error) {
      console.error('Failed to remove email from blacklist:', error);
      alert('Failed to remove email from blacklist');
    }
  };

  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Email Blacklist</CardTitle>
        <CardDescription>
          Manage emails that are automatically blocked from making return
          requests. Emails with more than 3 return requests are automatically
          added to this list.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <div className="flex-1">
            <Label htmlFor="new-email">Add Email to Blacklist</Label>
            <Input
              id="new-email"
              type="email"
              placeholder="Enter email address"
              value={newEmail}
              onChange={(e) => setNewEmail(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAddEmail()}
            />
          </div>
          <div className="flex items-end">
            <Button
              onClick={handleAddEmail}
              disabled={isAdding || !newEmail.trim()}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add
            </Button>
          </div>
        </div>

        <div>
          <Label>Blacklisted Emails ({blacklistedEmails.length})</Label>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            </div>
          ) : blacklistedEmails.length === 0 ? (
            <div className="py-8 text-center text-muted-foreground">
              No emails are currently blacklisted
            </div>
          ) : (
            <div className="mt-2 flex flex-wrap gap-2">
              {blacklistedEmails.map((email) => (
                <Badge
                  key={email}
                  variant="secondary"
                  className="flex items-center gap-2"
                >
                  {email}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                    onClick={() => handleRemoveEmail(email)}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
